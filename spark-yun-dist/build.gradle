// 构建lib文件夹
tasks.register('build_agent_lib', Copy) {

    dependsOn(":spark-yun-agent:bootJar")
    dependsOn(":spark-yun-backend:spark-yun-api:jar")

    def dependencies = configurations.runtimeClasspath.findAll {
        it.name.contains('fastjson2') ||
                it.name.contains('fastjson') ||
                it.name.contains('spark-yun-api') ||
                it.name.contains('log4j-api') ||
                it.name.contains('spark-core') ||
                it.name.contains('jetty-server') ||
                it.name.contains('jetty-util') ||
                it.name.contains('jetty-http') ||
                it.name.contains('jetty-io')
    }
    dependencies.each { dependency ->
        from dependency
        into 'build/zhiqingyun_agent/lib'
    }

    from rootDir.getAbsolutePath() + '/spark-yun-backend/spark-yun-api/build/libs'
    from rootDir.getAbsolutePath() + '/spark-yun-agent/build/libs'
    from rootDir.getAbsolutePath() + '/resources/jdbc/system'
    into 'build/zhiqingyun-agent/lib'
}

// 构建plugins文件夹
tasks.register('build_agent_plugins', Copy) {

    dependsOn(':spark-yun-plugins:spark-query-sql-plugin:jar')
    dependsOn(':spark-yun-plugins:spark-data-sync-jdbc-plugin:jar')
    dependsOn(':spark-yun-plugins:spark-excel-sync-jdbc-plugin:jar')
    dependsOn(':spark-yun-vip:spark-yun-plugins:spark-container-sql-plugin:jar')
    dependsOn(':spark-yun-vip:spark-yun-plugins:spark-real-sync-plugin:jar')
    dependsOn(':spark-yun-vip:spark-yun-plugins:spark-db-migrator-plugin:jar')

    from rootDir.getAbsolutePath() + '/spark-yun-plugins/spark-query-sql-plugin/build/libs'
    from rootDir.getAbsolutePath() + '/spark-yun-plugins/spark-data-sync-jdbc-plugin/build/libs'
    from rootDir.getAbsolutePath() + '/spark-yun-plugins/spark-excel-sync-jdbc-plugin/build/libs'
    from rootDir.getAbsolutePath() + '/spark-yun-vip/spark-yun-plugins/spark-container-sql-plugin/build/libs'
    from rootDir.getAbsolutePath() + '/spark-yun-vip/spark-yun-plugins/spark-real-sync-plugin/build/libs'
    from rootDir.getAbsolutePath() + '/spark-yun-vip/spark-yun-plugins/spark-db-migrator-plugin/build/libs'
    into 'build/zhiqingyun-agent/plugins'
}

// 构建至轻云代理
tasks.register('build_agent', Tar) {

    mustRunAfter(":spark-yun-frontend:make")

    dependsOn('build_agent_lib')
    dependsOn('build_agent_plugins')

    compression = Compression.GZIP
    archiveFileName = 'zhiqingyun-agent.tar.gz'

    from(rootDir.getAbsolutePath() + "/spark-yun-dist/spark-min") {
        into 'zhiqingyun-agent/spark-min'
    }
    from('zhiqingyun-agent/bin') {
        into 'zhiqingyun-agent/bin'
    }
    from(rootDir.getAbsolutePath() + "/spark-yun-agent/src/main/resources/application.yml") {
        into 'zhiqingyun-agent/conf'
    }
    from('zhiqingyun-agent/logs') {
        into 'zhiqingyun-agent/logs'
    }
    from('build/zhiqingyun-agent/plugins') {
        into 'zhiqingyun-agent/plugins'
    }
    from('zhiqingyun-agent/works') {
        into 'zhiqingyun-agent/works'
    }
    from('zhiqingyun-agent/file') {
        into 'zhiqingyun-agent/file'
    }
    from('build/zhiqingyun-agent/lib') {
        into 'zhiqingyun-agent/lib'
    }
    from('../README.md') {
        into 'zhiqingyun-agent/'
    }
}

// 构建至轻云
tasks.register('build_zhiqingyun', Tar) {

    mustRunAfter(":spark-yun-backend:make")

    compression = Compression.GZIP
    archiveFileName = 'zhiqingyun.tar.gz'

    from('zhiqingyun/bin') {
        into 'zhiqingyun/bin'
    }
    from('zhiqingyun/conf') {
        into 'zhiqingyun/conf'
    }
    from(rootDir.getAbsolutePath() + '/spark-yun-backend/spark-yun-main/src/main/resources/application-local.yml') {
        into 'zhiqingyun/conf'
    }
    from(rootDir.getAbsolutePath() + '/resources/jdbc/system') {
        into 'zhiqingyun/resources/jdbc/system'
    }
    from(rootDir.getAbsolutePath() + '/spark-yun-backend/spark-yun-main/build/libs/zhiqingyun.jar') {
        into 'zhiqingyun/lib'
    }
    from(rootDir.getAbsolutePath() + '/README.md') {
        into 'zhiqingyun/'
    }
}

// 打包
tasks.register('make', GradleBuild) {

    tasks = [":spark-yun-frontend:make", "build_agent", ":spark-yun-backend:make", "build_zhiqingyun"]
}

// 添加依赖
dependencies {

    implementation 'com.sparkjava:spark-core:2.9.4'

    implementation group: 'org.eclipse.jetty', name: 'jetty-server', version: '9.4.51.v20230217'
}