<template>
  <div id="app">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <FullScreenLoading
      ref="fullScreenLoadingRef"
      :title="loadingState.title"
      :message="loadingState.message"
      :progress="loadingState.progress"
    />
  </div>
</template>

<script setup lang="ts">
const fullScreenLoadingRef = ref(null);
const { setInstance, state: loadingState } = useFullScreenLoading();

onMounted(() => {
  console.log('App mounted, setting up loading instance');
  if (fullScreenLoadingRef.value) {
    setInstance(fullScreenLoadingRef.value);
    console.log('Loading instance set successfully');
  } else {
    console.error('Failed to get loading ref');
  }
});

// 设置页面标题和meta信息
useHead({
  title: '至轻云 - 企业级数据平台',
  meta: [
    { name: 'description', content: '至轻云是一个开源的企业级数据平台，提供数据集成、数据开发、数据治理等功能。' },
    { name: 'keywords', content: '至轻云,数据平台,大数据,开源,企业级' },
    { name: 'author', content: '至爻数据' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { property: 'og:title', content: '至轻云 - 企业级数据平台' },
    { property: 'og:description', content: '至轻云是一个开源的企业级数据平台，提供数据集成、数据开发、数据治理等功能。' },
    { property: 'og:type', content: 'website' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '至轻云 - 企业级数据平台' },
    { name: 'twitter:description', content: '至轻云是一个开源的企业级数据平台，提供数据集成、数据开发、数据治理等功能。' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
    { rel: 'preconnect', href: 'https://isxcode.oss-cn-shanghai.aliyuncs.com' },
    { rel: 'dns-prefetch', href: 'https://isxcode.oss-cn-shanghai.aliyuncs.com' }
  ]
});

// 监听页面加载状态
const nuxtApp = useNuxtApp();

// 页面开始加载时显示loading
nuxtApp.hook('page:start', () => {
  console.log('Page loading started');
});

// 页面加载完成时隐藏loading
nuxtApp.hook('page:finish', () => {
  console.log('Page loading finished');
});

// 监听应用错误
nuxtApp.hook('vue:error', (error) => {
  console.error('Vue error:', error);
});
</script>

<style>
/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fffaf8;
  color: #2c3e50;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  position: relative;
}

/* 优化图片加载 */
img {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

/* 优化链接样式 */
a {
  color: #e25a1b;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #ff7b3d;
}

/* 优化按钮样式 */
button {
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
}

/* 优化输入框样式 */
input, textarea, select {
  outline: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  transition: border-color 0.3s ease;
}

input:focus, textarea:focus, select:focus {
  border-color: #e25a1b;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 加载状态样式 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  body {
    background-color: #ffffff;
    color: #000000;
  }
  
  a {
    color: #0000ff;
  }
  
  a:hover {
    color: #ff0000;
  }
}
</style>
