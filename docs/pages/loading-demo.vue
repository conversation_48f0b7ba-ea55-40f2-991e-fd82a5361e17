<template>
  <div class="loading-demo">
    <LayoutHomeHeader />
    
    <div class="demo-container">
      <h1>Loading 动画演示</h1>
      <p>这个页面展示了各种loading动画效果</p>
      
      <div class="demo-section">
        <h2>1. 顶部进度条</h2>
        <p>页面顶部的进度条，用于显示页面加载进度</p>
        <div class="demo-buttons">
          <button @click="startTopLoading" class="demo-btn">开始加载</button>
          <button @click="finishTopLoading" class="demo-btn">完成加载</button>
          <button @click="errorTopLoading" class="demo-btn error">错误状态</button>
        </div>
      </div>

      <div class="demo-section">
        <h2>2. 全屏Loading</h2>
        <p>全屏遮罩loading，适用于页面初始化或重要操作</p>
        <div class="demo-buttons">
          <button @click="showFullScreenLoading" class="demo-btn">显示全屏Loading</button>
          <button @click="showProgressLoading" class="demo-btn">模拟进度加载</button>
        </div>
      </div>

      <div class="demo-section">
        <h2>3. 小型加载器</h2>
        <p>轻量级的加载器，适用于局部区域</p>
        
        <div class="spinner-grid">
          <div class="spinner-item">
            <h4>环形加载器</h4>
            <SpinnerLoading type="ring" size="medium" text="加载中..." />
          </div>
          
          <div class="spinner-item">
            <h4>点状加载器</h4>
            <SpinnerLoading type="dots" size="medium" text="处理中..." />
          </div>
          
          <div class="spinner-item">
            <h4>脉冲加载器</h4>
            <SpinnerLoading type="pulse" size="medium" text="请稍候..." />
          </div>
          
          <div class="spinner-item">
            <h4>条形加载器</h4>
            <SpinnerLoading type="bars" size="medium" text="同步中..." />
          </div>
        </div>

        <div class="size-demo">
          <h4>不同尺寸</h4>
          <div class="size-row">
            <SpinnerLoading type="ring" size="small" text="小" />
            <SpinnerLoading type="ring" size="medium" text="中" />
            <SpinnerLoading type="ring" size="large" text="大" />
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h2>4. 带遮罩的加载器</h2>
        <p>在容器内显示遮罩loading</p>
        <div class="overlay-demo" :class="{ 'loading': showOverlay }">
          <div class="demo-content">
            <h4>这是一个演示区域</h4>
            <p>点击下面的按钮来显示遮罩loading效果</p>
            <button @click="toggleOverlay" class="demo-btn">
              {{ showOverlay ? '隐藏' : '显示' }} 遮罩Loading
            </button>
          </div>
          <SpinnerLoading 
            v-if="showOverlay" 
            type="ring" 
            size="large" 
            text="加载中..." 
            overlay 
          />
        </div>
      </div>

      <div class="demo-section">
        <h2>5. 模拟资源加载</h2>
        <p>模拟真实的资源加载场景</p>
        <div class="resource-demo">
          <button @click="loadImages" class="demo-btn">加载图片资源</button>
          <div class="image-container" v-if="showImages">
            <img 
              v-for="(url, index) in imageUrls" 
              :key="index"
              :src="url" 
              :alt="`Demo image ${index + 1}`"
              class="demo-image"
              @load="onImageLoad"
              @error="onImageError"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'default'
});

const { start, finish, error } = useTopLoading();
const { show, hide, updateProgress, updateMessage, simulateProgress } = useFullScreenLoading();

const showOverlay = ref(false);
const showImages = ref(false);
const loadedImages = ref(0);

// 演示图片URLs
const imageUrls = [
  'https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/website/product-img/p-1.jpg',
  'https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/website/product-img/p-2.jpg',
  'https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/website/product-img/p-3.jpg',
  'https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/website/product-img/p-4.jpg'
];

// 顶部进度条演示
const startTopLoading = () => {
  start();
  // 3秒后自动完成
  setTimeout(() => {
    finish();
  }, 3000);
};

const finishTopLoading = () => {
  finish();
};

const errorTopLoading = () => {
  error();
};

// 全屏loading演示
const showFullScreenLoading = () => {
  show({
    title: '演示Loading',
    message: '这是一个演示用的全屏loading...'
  });
  
  // 3秒后自动隐藏
  setTimeout(() => {
    hide();
  }, 3000);
};

const showProgressLoading = () => {
  show({
    title: '模拟进度',
    message: '正在处理您的请求...'
  });
  
  // 模拟进度更新
  let progress = 0;
  const interval = setInterval(() => {
    progress += Math.random() * 15;
    if (progress >= 100) {
      progress = 100;
      clearInterval(interval);
      updateMessage('处理完成！');
      setTimeout(() => {
        hide();
      }, 1000);
    }
    updateProgress(progress);
    updateMessage(`处理进度: ${Math.round(progress)}%`);
  }, 300);
};

// 遮罩loading演示
const toggleOverlay = () => {
  showOverlay.value = !showOverlay.value;
  if (showOverlay.value) {
    // 3秒后自动隐藏
    setTimeout(() => {
      showOverlay.value = false;
    }, 3000);
  }
};

// 图片加载演示
const loadImages = () => {
  showImages.value = true;
  loadedImages.value = 0;
  
  show({
    title: '加载图片',
    message: '正在加载演示图片...'
  });
};

const onImageLoad = () => {
  loadedImages.value++;
  const progress = (loadedImages.value / imageUrls.length) * 100;
  updateProgress(progress);
  updateMessage(`已加载 ${loadedImages.value}/${imageUrls.length} 张图片`);
  
  if (loadedImages.value >= imageUrls.length) {
    setTimeout(() => {
      hide();
    }, 1000);
  }
};

const onImageError = () => {
  loadedImages.value++;
  const progress = (loadedImages.value / imageUrls.length) * 100;
  updateProgress(progress);
  updateMessage(`图片加载失败，继续加载其他图片... ${loadedImages.value}/${imageUrls.length}`);
  
  if (loadedImages.value >= imageUrls.length) {
    setTimeout(() => {
      hide();
    }, 1000);
  }
};

// 页面标题
useHead({
  title: 'Loading 动画演示 - 至轻云'
});
</script>

<style scoped>
.loading-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #fffaf8 0%, #fff5f0 100%);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 100px 20px 40px;
}

h1 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #e25a1b, #ff7b3d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-container > p {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 3rem;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 90, 27, 0.1);
}

.demo-section h2 {
  color: #e25a1b;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.demo-section p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.demo-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background: #e25a1b;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(226, 90, 27, 0.3);
}

.demo-btn:hover {
  background: #ff7b3d;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(226, 90, 27, 0.4);
}

.demo-btn.error {
  background: #ff4d4f;
}

.demo-btn.error:hover {
  background: #ff7875;
}

.spinner-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.spinner-item {
  text-align: center;
  padding: 1.5rem;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fafafa;
}

.spinner-item h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1rem;
}

.size-demo {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.size-demo h4 {
  margin-bottom: 1rem;
  color: #333;
}

.size-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
}

.overlay-demo {
  position: relative;
  min-height: 200px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background: #f9f9f9;
  overflow: hidden;
}

.demo-content {
  padding: 2rem;
  text-align: center;
}

.demo-content h4 {
  margin-bottom: 1rem;
  color: #333;
}

.demo-content p {
  margin-bottom: 1.5rem;
  color: #666;
}

.resource-demo {
  text-align: center;
}

.image-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.demo-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.demo-image:hover {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-container {
    padding: 80px 15px 20px;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .demo-section {
    padding: 1.5rem;
  }
  
  .demo-buttons {
    justify-content: center;
  }
  
  .spinner-grid {
    grid-template-columns: 1fr;
  }
  
  .size-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .image-container {
    grid-template-columns: 1fr;
  }
}
</style>
