interface FullScreenLoadingInstance {
  show: () => void;
  hide: () => void;
}

interface LoadingState {
  isVisible: boolean;
  title: string;
  message: string;
  progress: number;
}

let fullScreenLoadingInstance: FullScreenLoadingInstance | null = null;
const loadingState = reactive<LoadingState>({
  isVisible: false,
  title: '至轻云',
  message: '正在加载资源，请稍候...',
  progress: 0
});

export const useFullScreenLoading = () => {
  // 设置loading实例
  const setInstance = (instance: FullScreenLoadingInstance) => {
    fullScreenLoadingInstance = instance;
  };

  // 显示全屏loading
  const show = (options?: {
    title?: string;
    message?: string;
  }) => {
    if (options?.title) {
      loadingState.title = options.title;
    }
    if (options?.message) {
      loadingState.message = options.message;
    }
    
    loadingState.isVisible = true;
    loadingState.progress = 0;
    
    if (fullScreenLoadingInstance) {
      fullScreenLoadingInstance.show();
    }
  };

  // 隐藏全屏loading
  const hide = () => {
    loadingState.isVisible = false;
    if (fullScreenLoadingInstance) {
      fullScreenLoadingInstance.hide();
    }
  };

  // 更新进度
  const updateProgress = (progress: number) => {
    loadingState.progress = Math.max(0, Math.min(100, progress));
  };

  // 更新消息
  const updateMessage = (message: string) => {
    loadingState.message = message;
  };

  // 更新标题
  const updateTitle = (title: string) => {
    loadingState.title = title;
  };

  // 模拟进度增长
  const simulateProgress = (duration: number = 3000) => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min((elapsed / duration) * 90, 90); // 最多到90%
      updateProgress(progress);
      
      if (progress >= 90) {
        clearInterval(interval);
      }
    }, 50);
    
    return interval;
  };

  return {
    setInstance,
    show,
    hide,
    updateProgress,
    updateMessage,
    updateTitle,
    simulateProgress,
    state: readonly(loadingState)
  };
};

// 资源加载检测器
export const useResourceLoadingDetector = () => {
  const { show, hide, updateProgress, updateMessage } = useFullScreenLoading();
  const { start: startTopLoading, finish: finishTopLoading } = useTopLoading();
  
  let loadingStarted = false;
  let resourcesLoaded = 0;
  let totalResources = 0;
  let progressInterval: NodeJS.Timeout | null = null;

  // 检测页面资源加载
  const detectPageResources = () => {
    if (loadingStarted) return;
    
    // 获取所有需要加载的资源
    const images = document.querySelectorAll('img[src]');
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    const scripts = document.querySelectorAll('script[src]');
    
    totalResources = images.length + stylesheets.length + scripts.length;
    
    // 如果有资源需要加载，显示loading
    if (totalResources > 0) {
      loadingStarted = true;
      show({
        title: '至轻云',
        message: `正在加载 ${totalResources} 个资源...`
      });
      startTopLoading();
      
      // 开始模拟进度
      progressInterval = simulateProgress(2000);
    }

    const checkResourceLoaded = () => {
      resourcesLoaded++;
      const progress = (resourcesLoaded / totalResources) * 100;
      updateProgress(progress);
      updateMessage(`已加载 ${resourcesLoaded}/${totalResources} 个资源`);
      
      if (resourcesLoaded >= totalResources) {
        setTimeout(() => {
          finishLoading();
        }, 300);
      }
    };

    // 监听图片加载
    images.forEach((img: HTMLImageElement) => {
      if (img.complete && img.naturalHeight !== 0) {
        checkResourceLoaded();
      } else {
        img.addEventListener('load', checkResourceLoaded);
        img.addEventListener('error', checkResourceLoaded);
      }
    });

    // 监听样式表加载
    stylesheets.forEach((link: HTMLLinkElement) => {
      if (link.sheet) {
        checkResourceLoaded();
      } else {
        link.addEventListener('load', checkResourceLoaded);
        link.addEventListener('error', checkResourceLoaded);
      }
    });

    // 监听脚本加载
    scripts.forEach((script: HTMLScriptElement) => {
      if (script.readyState === 'complete') {
        checkResourceLoaded();
      } else {
        script.addEventListener('load', checkResourceLoaded);
        script.addEventListener('error', checkResourceLoaded);
      }
    });

    // 如果没有资源需要加载
    if (totalResources === 0) {
      setTimeout(() => {
        finishLoading();
      }, 500);
    }
  };

  // 完成加载
  const finishLoading = () => {
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }
    
    updateProgress(100);
    updateMessage('加载完成');
    finishTopLoading();
    
    setTimeout(() => {
      hide();
      loadingStarted = false;
      resourcesLoaded = 0;
      totalResources = 0;
    }, 800);
  };

  // 监听DOM变化，处理动态加载的资源
  const observeNewResources = () => {
    const observer = new MutationObserver((mutations) => {
      let hasNewResources = false;

      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // 检查是否是图片或包含图片
            if (element.tagName === 'IMG' || element.querySelectorAll('img[src]').length > 0) {
              hasNewResources = true;
            }

            // 检查是否是样式表或脚本
            if (element.tagName === 'LINK' || element.tagName === 'SCRIPT') {
              hasNewResources = true;
            }
          }
        });
      });

      if (hasNewResources && !loadingStarted) {
        // 延迟一点时间让新资源开始加载
        setTimeout(() => {
          detectPageResources();
        }, 100);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return observer;
  };

  return {
    detectPageResources,
    observeNewResources,
    finishLoading
  };
};
