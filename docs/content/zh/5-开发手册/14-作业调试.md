---
title: "作业调试"
---

## 本地调试自定义作业
 
> 案例：解析本地的json文件数据

#### 下载demo代码

```bash
git clone https://github.com/isxcode/spark-job-template.git
```

#### 创建json数据文件

```bash
vim /Users/<USER>/Downloads/test.json
```

```json
{"age": 13,"username": "张三"}
{"age": 14,"username": "李四"}
```

#### 编写代码

```bash
vim spark-job-template/src/main/java/com/isxcode/star/job/Execute.java
```

```java
package com.isxcode.star.job;

import org.apache.spark.SparkConf;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

public class Execute {

    public static void main(String[] args) {

        try (SparkSession sparkSession = initSparkSession()) {
            Dataset<Row> dataset = sparkSession.read().json("/Users/<USER>/Downloads/test.json");
            dataset.show();
            dataset.collectAsList().forEach(e -> {
                for (int i = 0; i < e.size(); i++) {
                    System.out.println(e.get(i));
                }
            });
        }
    }

    /**
     * 初始化本地sparkSession.
     */
    public static SparkSession initSparkSession() {

        SparkSession.Builder sparkSessionBuilder = SparkSession.builder();
        SparkConf conf = new SparkConf();
        conf.set("spark.master", "local[*]");
        return sparkSessionBuilder.config(conf).getOrCreate();
    }
}
```

![20250117175302](https://img.isxcode.com/picgo/20250117175302.png)

![20250117175230](https://img.isxcode.com/picgo/20250117175230.png)

#### 总结

```wikitext
在spark的config配置中，使用以下代码即可实现本地调试
```

```java
conf.set("spark.master", "local[*]");
```