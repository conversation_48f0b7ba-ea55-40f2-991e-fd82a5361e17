export default defineNuxtPlugin(() => {
  if (process.client) {
    console.log('Time-based loading plugin started');
    
    // 创建loading元素
    const createLoadingElement = () => {
      const loadingDiv = document.createElement('div');
      loadingDiv.id = 'time-loading';
      loadingDiv.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 250, 248, 0.95);
          backdrop-filter: blur(10px);
          z-index: 9999;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        ">
          <div style="
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
          ">
            <div style="
              width: 60px;
              height: 60px;
              margin: 0 auto 1.5rem;
              border: 3px solid #f3f3f3;
              border-top: 3px solid #e25a1b;
              border-radius: 50%;
              animation: spin 1s linear infinite;
            "></div>
            <h3 style="
              margin: 0 0 0.5rem;
              font-size: 1.5rem;
              color: #2c3e50;
              background: linear-gradient(45deg, #e25a1b, #ff7b3d);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            ">至轻云</h3>
            <p style="
              margin: 0;
              color: #666;
              font-size: 0.9rem;
            " id="time-loading-message">正在加载页面...</p>
            <div style="
              width: 100%;
              height: 4px;
              background: #f0f0f0;
              border-radius: 2px;
              margin-top: 1rem;
              overflow: hidden;
            ">
              <div id="time-loading-progress" style="
                height: 100%;
                background: linear-gradient(90deg, #e25a1b 0%, #ff7b3d 50%, #e25a1b 100%);
                background-size: 200% 100%;
                width: 0%;
                border-radius: 2px;
                transition: width 0.3s ease;
                animation: shimmer 1.5s infinite;
              "></div>
            </div>
          </div>
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        </style>
      `;
      return loadingDiv;
    };

    // 显示loading
    const showLoading = () => {
      const existing = document.getElementById('time-loading');
      if (!existing) {
        const loadingElement = createLoadingElement();
        document.body.appendChild(loadingElement);
        console.log('Loading element added to DOM');
      }
    };

    // 隐藏loading
    const hideLoading = () => {
      const loadingElement = document.getElementById('time-loading');
      if (loadingElement) {
        loadingElement.style.opacity = '0';
        loadingElement.style.transition = 'opacity 0.5s ease';
        setTimeout(() => {
          if (loadingElement.parentNode) {
            loadingElement.parentNode.removeChild(loadingElement);
            console.log('Loading element removed from DOM');
          }
        }, 500);
      }
    };

    // 更新进度
    const updateProgress = (progress: number) => {
      const progressBar = document.getElementById('time-loading-progress');
      if (progressBar) {
        progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
      }
    };

    // 更新消息
    const updateMessage = (message: string) => {
      const messageElement = document.getElementById('time-loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
    };

    // 基于时间的loading进度模拟
    const startTimeBasedLoading = (duration: number = 2000) => {
      const startTime = Date.now();
      let animationId: number;
      
      const messages = [
        '正在加载页面...',
        '正在加载资源...',
        '正在渲染内容...',
        '即将完成...'
      ];
      
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / duration) * 100, 95); // 最多到95%
        
        updateProgress(progress);
        
        // 根据进度更新消息
        const messageIndex = Math.floor((progress / 100) * messages.length);
        if (messageIndex < messages.length) {
          updateMessage(messages[messageIndex]);
        }
        
        if (progress < 95) {
          animationId = requestAnimationFrame(animate);
        } else {
          // 等待真实的页面加载完成
          if (document.readyState === 'complete') {
            updateProgress(100);
            updateMessage('加载完成');
            setTimeout(hideLoading, 800);
          } else {
            // 继续等待页面完成
            window.addEventListener('load', () => {
              updateProgress(100);
              updateMessage('加载完成');
              setTimeout(hideLoading, 800);
            }, { once: true });
          }
        }
      };
      
      animationId = requestAnimationFrame(animate);
      
      // 最大超时保护
      setTimeout(() => {
        if (animationId) {
          cancelAnimationFrame(animationId);
        }
        updateProgress(100);
        updateMessage('加载完成');
        setTimeout(hideLoading, 800);
      }, duration + 2000);
    };

    // 检查是否有大量图片需要加载
    const getLoadingDuration = () => {
      const images = document.querySelectorAll('img[src]');
      const baseTime = 1500; // 基础时间1.5秒
      const imageTime = images.length * 100; // 每张图片增加100ms
      return Math.min(baseTime + imageTime, 4000); // 最多4秒
    };

    // 立即显示loading
    showLoading();
    
    // 开始基于时间的loading
    const loadingDuration = getLoadingDuration();
    console.log(`Starting time-based loading for ${loadingDuration}ms`);
    startTimeBasedLoading(loadingDuration);

    // 路由变化监听
    const router = useRouter();
    router.beforeEach((to, from) => {
      if (to.path !== from.path) {
        console.log('Route changing, showing loading');
        showLoading();
        const duration = getLoadingDuration();
        startTimeBasedLoading(duration);
      }
    });

    // 页面完全加载后确保隐藏loading
    window.addEventListener('load', () => {
      console.log('Window load event fired');
      setTimeout(() => {
        updateProgress(100);
        updateMessage('页面加载完成');
        setTimeout(hideLoading, 1000);
      }, 500);
    });

    // 页面可见性变化时的处理
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // 页面重新可见时，如果loading还在显示，给它一个超时
        const loadingElement = document.getElementById('time-loading');
        if (loadingElement) {
          setTimeout(() => {
            hideLoading();
          }, 2000);
        }
      }
    });
  }
});
