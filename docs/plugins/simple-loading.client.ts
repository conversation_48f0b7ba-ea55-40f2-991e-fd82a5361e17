export default defineNuxtPlugin(() => {
  if (process.client) {
    console.log('Simple loading plugin started');
    
    // 创建loading元素
    const createLoadingElement = () => {
      const loadingDiv = document.createElement('div');
      loadingDiv.id = 'simple-loading';
      loadingDiv.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 250, 248, 0.95);
          backdrop-filter: blur(10px);
          z-index: 9999;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        ">
          <div style="
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
          ">
            <div style="
              width: 60px;
              height: 60px;
              margin: 0 auto 1.5rem;
              border: 3px solid #f3f3f3;
              border-top: 3px solid #e25a1b;
              border-radius: 50%;
              animation: spin 1s linear infinite;
            "></div>
            <h3 style="
              margin: 0 0 0.5rem;
              font-size: 1.5rem;
              color: #2c3e50;
              background: linear-gradient(45deg, #e25a1b, #ff7b3d);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            ">至轻云</h3>
            <p style="
              margin: 0;
              color: #666;
              font-size: 0.9rem;
            " id="loading-message">正在加载资源，请稍候...</p>
            <div style="
              width: 100%;
              height: 4px;
              background: #f0f0f0;
              border-radius: 2px;
              margin-top: 1rem;
              overflow: hidden;
            ">
              <div id="loading-progress" style="
                height: 100%;
                background: linear-gradient(90deg, #e25a1b 0%, #ff7b3d 50%, #e25a1b 100%);
                background-size: 200% 100%;
                width: 0%;
                border-radius: 2px;
                transition: width 0.3s ease;
                animation: shimmer 1.5s infinite;
              "></div>
            </div>
          </div>
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        </style>
      `;
      return loadingDiv;
    };

    // 显示loading
    const showLoading = () => {
      const existing = document.getElementById('simple-loading');
      if (!existing) {
        const loadingElement = createLoadingElement();
        document.body.appendChild(loadingElement);
        console.log('Loading element added to DOM');
      }
    };

    // 隐藏loading
    const hideLoading = () => {
      const loadingElement = document.getElementById('simple-loading');
      if (loadingElement) {
        loadingElement.style.opacity = '0';
        loadingElement.style.transition = 'opacity 0.5s ease';
        setTimeout(() => {
          if (loadingElement.parentNode) {
            loadingElement.parentNode.removeChild(loadingElement);
            console.log('Loading element removed from DOM');
          }
        }, 500);
      }
    };

    // 更新进度
    const updateProgress = (progress: number) => {
      const progressBar = document.getElementById('loading-progress');
      if (progressBar) {
        progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
      }
    };

    // 更新消息
    const updateMessage = (message: string) => {
      const messageElement = document.getElementById('loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
    };

    // 检测资源加载
    const detectResources = () => {
      const images = document.querySelectorAll('img[src]');
      const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
      const scripts = document.querySelectorAll('script[src]');

      const totalResources = images.length + stylesheets.length + scripts.length;
      let loadedResources = 0;
      let timeoutId: NodeJS.Timeout;

      console.log(`检测到 ${totalResources} 个资源需要加载`);

      if (totalResources === 0) {
        updateMessage('页面加载完成');
        updateProgress(100);
        setTimeout(hideLoading, 800);
        return;
      }

      updateMessage(`正在加载 ${totalResources} 个资源...`);

      // 设置超时机制，最多等待10秒
      timeoutId = setTimeout(() => {
        console.log('Loading timeout reached, forcing completion');
        updateMessage('加载完成');
        updateProgress(100);
        setTimeout(hideLoading, 1000);
      }, 10000);

      const checkLoaded = () => {
        loadedResources++;
        const progress = (loadedResources / totalResources) * 100;
        updateProgress(progress);
        updateMessage(`已加载 ${loadedResources}/${totalResources} 个资源`);

        console.log(`资源加载进度: ${loadedResources}/${totalResources} (${Math.round(progress)}%)`);

        if (loadedResources >= totalResources) {
          clearTimeout(timeoutId);
          updateMessage('加载完成');
          setTimeout(hideLoading, 1000);
        }
      };

      // 监听图片加载
      images.forEach((img: HTMLImageElement, index) => {
        if (img.complete && img.naturalHeight !== 0) {
          console.log(`Image ${index + 1} already loaded:`, img.src);
          checkLoaded();
        } else {
          const loadHandler = () => {
            console.log(`Image ${index + 1} loaded:`, img.src);
            checkLoaded();
          };
          const errorHandler = () => {
            console.log(`Image ${index + 1} failed to load:`, img.src);
            checkLoaded();
          };

          img.addEventListener('load', loadHandler, { once: true });
          img.addEventListener('error', errorHandler, { once: true });

          // 为每个图片设置单独的超时
          setTimeout(() => {
            if (!img.complete) {
              console.log(`Image ${index + 1} timeout:`, img.src);
              checkLoaded();
            }
          }, 5000);
        }
      });

      // 监听样式表加载
      stylesheets.forEach((link: HTMLLinkElement, index) => {
        if (link.sheet) {
          console.log(`Stylesheet ${index + 1} already loaded:`, link.href);
          checkLoaded();
        } else {
          const loadHandler = () => {
            console.log(`Stylesheet ${index + 1} loaded:`, link.href);
            checkLoaded();
          };
          const errorHandler = () => {
            console.log(`Stylesheet ${index + 1} failed to load:`, link.href);
            checkLoaded();
          };

          link.addEventListener('load', loadHandler, { once: true });
          link.addEventListener('error', errorHandler, { once: true });

          // 为每个样式表设置超时
          setTimeout(() => {
            if (!link.sheet) {
              console.log(`Stylesheet ${index + 1} timeout:`, link.href);
              checkLoaded();
            }
          }, 3000);
        }
      });

      // 监听脚本加载
      scripts.forEach((script: HTMLScriptElement, index) => {
        const loadHandler = () => {
          console.log(`Script ${index + 1} loaded:`, script.src);
          checkLoaded();
        };
        const errorHandler = () => {
          console.log(`Script ${index + 1} failed to load:`, script.src);
          checkLoaded();
        };

        script.addEventListener('load', loadHandler, { once: true });
        script.addEventListener('error', errorHandler, { once: true });

        // 为每个脚本设置超时
        setTimeout(() => {
          console.log(`Script ${index + 1} timeout check:`, script.src);
          checkLoaded();
        }, 3000);
      });
    };

    // 立即显示loading
    showLoading();

    // 等待DOM准备好后检测资源
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM Content Loaded');
        setTimeout(detectResources, 100);
      });
    } else {
      console.log('DOM already ready');
      setTimeout(detectResources, 100);
    }

    // 监听页面完全加载
    window.addEventListener('load', () => {
      console.log('Window load event fired');
      setTimeout(() => {
        updateMessage('页面加载完成');
        updateProgress(100);
        setTimeout(hideLoading, 1000);
      }, 500);
    });

    // 路由变化监听
    const router = useRouter();
    router.beforeEach((to, from) => {
      if (to.path !== from.path) {
        console.log('Route changing, showing loading');
        showLoading();
        updateProgress(0);
        updateMessage('正在加载页面...');
      }
    });

    router.afterEach(() => {
      console.log('Route changed, detecting resources');
      setTimeout(detectResources, 200);
    });
  }
});
