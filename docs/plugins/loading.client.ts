export default defineNuxtPlugin(() => {
  const { start, finish, error } = useTopLoading();
  const { detectPageResources, observeNewResources: observeResources } = useResourceLoadingDetector();

  // 监听页面资源加载
  const handleResourceLoading = () => {
    let loadingStarted = false;
    let resourcesLoaded = 0;
    let totalResources = 0;

    // 获取所有需要加载的资源
    const images = document.querySelectorAll('img');
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    const scripts = document.querySelectorAll('script[src]');

    totalResources = images.length + stylesheets.length + scripts.length;

    // 如果有资源需要加载，显示loading
    if (totalResources > 0 && !loadingStarted) {
      start();
      loadingStarted = true;
    }

    const checkAllLoaded = () => {
      resourcesLoaded++;
      if (resourcesLoaded >= totalResources && loadingStarted) {
        setTimeout(() => {
          finish();
        }, 200);
      }
    };

    // 监听图片加载
    images.forEach((img) => {
      if (img.complete) {
        checkAllLoaded();
      } else {
        img.addEventListener('load', checkAllLoaded);
        img.addEventListener('error', () => {
          checkAllLoaded();
        });
      }
    });

    // 监听样式表加载
    stylesheets.forEach((link) => {
      if (link.sheet) {
        checkAllLoaded();
      } else {
        link.addEventListener('load', checkAllLoaded);
        link.addEventListener('error', checkAllLoaded);
      }
    });

    // 监听脚本加载
    scripts.forEach((script) => {
      script.addEventListener('load', checkAllLoaded);
      script.addEventListener('error', checkAllLoaded);
    });

    // 如果没有资源需要加载，直接完成
    if (totalResources === 0 && loadingStarted) {
      setTimeout(() => {
        finish();
      }, 100);
    }
  };

  // 页面加载完成后开始监听
  onMounted(() => {
    // 使用新的资源检测器
    detectPageResources();

    // 开始监听新资源
    const observer = observeResources();

    // 清理观察器
    onBeforeUnmount(() => {
      observer.disconnect();
    });
  });

  // 监听路由变化，在页面切换时显示loading
  const router = useRouter();
  router.beforeEach((to, from) => {
    if (to.path !== from.path) {
      start();
    }
  });

  router.afterEach(() => {
    // 延迟一点时间确保页面渲染完成
    setTimeout(() => {
      detectPageResources();
    }, 100);
  });
});