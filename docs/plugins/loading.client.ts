export default defineNuxtPlugin(() => {
  const { start, finish, error } = useTopLoading();
  const { show, hide, updateProgress, updateMessage } = useFullScreenLoading();

  // 监听页面资源加载
  const handleResourceLoading = () => {
    let loadingStarted = false;
    let resourcesLoaded = 0;
    let totalResources = 0;

    // 获取所有需要加载的资源
    const images = document.querySelectorAll('img[src]');
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    const scripts = document.querySelectorAll('script[src]');

    totalResources = images.length + stylesheets.length + scripts.length;

    console.log('检测到资源数量:', totalResources, { images: images.length, stylesheets: stylesheets.length, scripts: scripts.length });

    // 如果有资源需要加载，显示loading
    if (totalResources > 0 && !loadingStarted) {
      start();
      show({
        title: '至轻云',
        message: `正在加载 ${totalResources} 个资源...`
      });
      loadingStarted = true;
      console.log('开始显示loading');
    }

    const checkAllLoaded = () => {
      resourcesLoaded++;
      const progress = (resourcesLoaded / totalResources) * 100;
      updateProgress(progress);
      updateMessage(`已加载 ${resourcesLoaded}/${totalResources} 个资源`);

      console.log(`资源加载进度: ${resourcesLoaded}/${totalResources} (${Math.round(progress)}%)`);

      if (resourcesLoaded >= totalResources && loadingStarted) {
        setTimeout(() => {
          finish();
          hide();
          console.log('所有资源加载完成，隐藏loading');
        }, 500);
      }
    };

    // 监听图片加载
    images.forEach((img) => {
      if (img.complete) {
        checkAllLoaded();
      } else {
        img.addEventListener('load', checkAllLoaded);
        img.addEventListener('error', () => {
          checkAllLoaded();
        });
      }
    });

    // 监听样式表加载
    stylesheets.forEach((link) => {
      if (link.sheet) {
        checkAllLoaded();
      } else {
        link.addEventListener('load', checkAllLoaded);
        link.addEventListener('error', checkAllLoaded);
      }
    });

    // 监听脚本加载
    scripts.forEach((script) => {
      script.addEventListener('load', checkAllLoaded);
      script.addEventListener('error', checkAllLoaded);
    });

    // 如果没有资源需要加载，显示一个短暂的loading然后完成
    if (totalResources === 0) {
      show({
        title: '至轻云',
        message: '页面加载中...'
      });
      start();
      setTimeout(() => {
        finish();
        hide();
        console.log('无资源需要加载，短暂显示后隐藏loading');
      }, 800);
    }
  };

  // 立即开始检测资源
  if (process.client) {
    console.log('Loading plugin initialized');

    // 页面加载时立即显示loading
    show({
      title: '至轻云',
      message: '正在初始化...'
    });
    start();

    // 等待DOM准备好后检测资源
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM Content Loaded, starting resource detection');
        handleResourceLoading();
      });
    } else {
      // DOM已经准备好
      console.log('DOM already ready, starting resource detection');
      setTimeout(() => {
        handleResourceLoading();
      }, 100);
    }
  }

  // 监听路由变化
  if (process.client) {
    const router = useRouter();
    router.beforeEach((to, from) => {
      if (to.path !== from.path) {
        console.log('Route changing from', from.path, 'to', to.path);
        start();
        show({
          title: '至轻云',
          message: '正在加载页面...'
        });
      }
    });

    router.afterEach(() => {
      console.log('Route changed, detecting resources');
      setTimeout(() => {
        handleResourceLoading();
      }, 200);
    });
  }
});