<template>
  <div
    v-show="isVisible"
    :class="[
      'top-loading-bar',
      {
        'top-loading-bar--loading': isLoading,
        'top-loading-bar--complete': isComplete,
        'top-loading-bar--error': isError
      }
    ]"
  >
    <div
      class="top-loading-bar__progress"
      :style="{ width: progress + '%' }"
    ></div>
  </div>
</template>

<script setup lang="ts">
interface LoadingBarState {
  isVisible: boolean;
  isLoading: boolean;
  isComplete: boolean;
  isError: boolean;
  progress: number;
}

const state = reactive<LoadingBarState>({
  isVisible: false,
  isLoading: false,
  isComplete: false,
  isError: false,
  progress: 0
});

const { isVisible, isLoading, isComplete, isError, progress } = toRefs(state);

let progressTimer: NodeJS.Timeout | null = null;
let hideTimer: NodeJS.Timeout | null = null;

// 开始loading
const start = () => {
  reset();
  state.isVisible = true;
  state.isLoading = true;
  state.progress = 0;

  // 模拟进度增长
  progressTimer = setInterval(() => {
    if (state.progress < 90) {
      const increment = Math.random() * 15;
      state.progress = Math.min(state.progress + increment, 90);
    }
  }, 200);
};

// 完成loading
const finish = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }

  state.progress = 100;
  state.isLoading = false;
  state.isComplete = true;

  // 延迟隐藏
  hideTimer = setTimeout(() => {
    hide();
  }, 300);
};

// 错误状态
const error = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }

  state.isLoading = false;
  state.isError = true;

  // 延迟隐藏
  hideTimer = setTimeout(() => {
    hide();
  }, 1000);
};

// 隐藏loading条
const hide = () => {
  state.isVisible = false;
  setTimeout(() => {
    reset();
  }, 300);
};

// 重置状态
const reset = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }

  state.isLoading = false;
  state.isComplete = false;
  state.isError = false;
  state.progress = 0;
};

// 暴露方法给父组件
defineExpose({
  start,
  finish,
  error,
  hide,
  reset
});

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  reset();
});
</script>

<style scoped>
.top-loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  z-index: 9999;
  background: transparent;
  transition: opacity 0.3s ease;
}

.top-loading-bar__progress {
  height: 100%;
  background: linear-gradient(90deg, #e25a1b 0%, #ff7b3d 50%, #e25a1b 100%);
  background-size: 200% 100%;
  border-radius: 0 2px 2px 0;
  transition: width 0.3s ease;
  animation: shimmer 1.5s infinite;
  box-shadow: 0 0 10px rgba(226, 90, 27, 0.5);
}

.top-loading-bar--loading .top-loading-bar__progress {
  animation: shimmer 1.5s infinite, pulse 2s infinite;
}

.top-loading-bar--complete .top-loading-bar__progress {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 50%, #52c41a 100%);
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
}

.top-loading-bar--error .top-loading-bar__progress {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 50%, #ff4d4f 100%);
  box-shadow: 0 0 10px rgba(255, 77, 79, 0.5);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
</style>