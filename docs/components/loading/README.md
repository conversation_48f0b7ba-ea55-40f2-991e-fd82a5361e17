# Loading 组件系统

本项目包含了一套完整的loading动画系统，用于提升用户访问官网的体验。

## 组件概览

### 1. TopLoadingBar - 顶部进度条
位置：`components/loading/TopLoadingBar.vue`

一个显示在页面顶部的进度条，用于显示页面加载进度。

**特性：**
- 自动进度模拟
- 完成和错误状态
- 平滑动画效果
- 主题色彩配置

**使用方法：**
```vue
<template>
  <TopLoadingBar ref="loadingBarRef" />
</template>

<script setup>
const loadingBarRef = ref(null);
const { setInstance, start, finish, error } = useTopLoading();

onMounted(() => {
  if (loadingBarRef.value) {
    setInstance(loadingBarRef.value);
  }
});

// 开始loading
start();

// 完成loading
finish();

// 错误状态
error();
</script>
```

### 2. FullScreenLoading - 全屏加载动画
位置：`components/loading/FullScreenLoading.vue`

一个全屏的loading动画，用于页面初始加载或重要操作。

**特性：**
- 全屏遮罩
- 进度显示
- 自定义标题和消息
- 响应式设计
- 优雅的进入/退出动画

**使用方法：**
```vue
<template>
  <FullScreenLoading 
    ref="fullScreenLoadingRef"
    :title="title"
    :message="message"
    :progress="progress"
  />
</template>

<script setup>
const fullScreenLoadingRef = ref(null);
const { setInstance, show, hide, updateProgress } = useFullScreenLoading();

onMounted(() => {
  if (fullScreenLoadingRef.value) {
    setInstance(fullScreenLoadingRef.value);
  }
});

// 显示loading
show({
  title: '加载中',
  message: '正在处理您的请求...'
});

// 更新进度
updateProgress(50);

// 隐藏loading
hide();
</script>
```

### 3. SpinnerLoading - 小型加载器
位置：`components/loading/SpinnerLoading.vue`

一个轻量级的加载器，适用于局部区域的加载状态。

**特性：**
- 多种动画类型（dots, ring, pulse, bars）
- 三种尺寸（small, medium, large）
- 可选遮罩层
- 自定义文本

**使用方法：**
```vue
<template>
  <!-- 基础用法 -->
  <SpinnerLoading />
  
  <!-- 自定义类型和尺寸 -->
  <SpinnerLoading type="dots" size="large" text="加载中..." />
  
  <!-- 带遮罩层 -->
  <SpinnerLoading type="ring" overlay />
</template>
```

## Composables

### 1. useTopLoading
位置：`composables/useTopLoading.ts`

管理顶部进度条的状态。

**方法：**
- `setInstance(instance)` - 设置loading实例
- `start()` - 开始loading
- `finish()` - 完成loading
- `error()` - 错误状态
- `hide()` - 隐藏loading
- `reset()` - 重置状态

### 2. useFullScreenLoading
位置：`composables/useFullScreenLoading.ts`

管理全屏loading的状态。

**方法：**
- `setInstance(instance)` - 设置loading实例
- `show(options)` - 显示loading
- `hide()` - 隐藏loading
- `updateProgress(progress)` - 更新进度
- `updateMessage(message)` - 更新消息
- `updateTitle(title)` - 更新标题
- `simulateProgress(duration)` - 模拟进度增长

### 3. useResourceLoadingDetector
位置：`composables/useFullScreenLoading.ts`

自动检测页面资源加载状态。

**方法：**
- `detectPageResources()` - 检测页面资源
- `observeNewResources()` - 监听新资源
- `finishLoading()` - 完成加载

## 插件

### loading.client.ts
位置：`plugins/loading.client.ts`

自动监听页面资源加载，包括：
- 图片加载
- 样式表加载
- 脚本加载
- 动态资源加载
- 路由切换

## 自动集成

系统已经在以下位置自动集成：

1. **app.vue** - 全屏loading组件
2. **layouts/default.vue** - 顶部进度条
3. **plugins/loading.client.ts** - 自动资源检测

## 配置选项

### 主题颜色
可以通过CSS变量自定义主题颜色：

```css
:root {
  --loading-primary: #e25a1b;
  --loading-secondary: #ff7b3d;
  --loading-success: #52c41a;
  --loading-warning: #faad14;
  --loading-error: #ff4d4f;
}
```

### 动画时长
可以通过CSS变量自定义动画时长：

```css
:root {
  --loading-duration: 0.3s;
  --loading-delay: 0.1s;
}
```

## 性能优化

1. **懒加载** - 组件支持懒加载
2. **防抖** - 避免频繁的loading状态切换
3. **内存清理** - 自动清理定时器和事件监听器
4. **响应式** - 适配不同屏幕尺寸

## 无障碍访问

1. **减少动画** - 支持 `prefers-reduced-motion`
2. **高对比度** - 支持高对比度模式
3. **键盘导航** - 支持键盘操作
4. **屏幕阅读器** - 提供适当的ARIA标签

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 故障排除

### 常见问题

1. **loading不显示**
   - 检查组件是否正确引入
   - 确认实例是否正确设置

2. **进度不更新**
   - 检查progress值是否在0-100范围内
   - 确认updateProgress方法调用正确

3. **动画卡顿**
   - 检查CSS动画是否被其他样式覆盖
   - 确认浏览器硬件加速是否开启

### 调试模式

可以在浏览器控制台中启用调试模式：

```javascript
// 启用loading调试
window.__LOADING_DEBUG__ = true;
```

这将在控制台输出详细的loading状态信息。
