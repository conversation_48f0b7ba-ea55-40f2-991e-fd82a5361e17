<template>
  <div class="spinner-loading" :class="[`spinner-${size}`, { 'spinner-overlay': overlay }]">
    <div v-if="overlay" class="spinner-backdrop"></div>
    <div class="spinner-container">
      <div class="spinner" :class="`spinner-${type}`">
        <div v-if="type === 'dots'" class="spinner-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div v-else-if="type === 'ring'" class="spinner-ring"></div>
        <div v-else-if="type === 'pulse'" class="spinner-pulse"></div>
        <div v-else-if="type === 'bars'" class="spinner-bars">
          <span></span>
          <span></span>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div v-if="text" class="spinner-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'dots' | 'ring' | 'pulse' | 'bars';
  size?: 'small' | 'medium' | 'large';
  text?: string;
  overlay?: boolean;
}

withDefaults(defineProps<Props>(), {
  type: 'ring',
  size: 'medium',
  text: '',
  overlay: false
});
</script>

<style scoped>
.spinner-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.spinner-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.spinner-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 尺寸变体 */
.spinner-small .spinner {
  width: 20px;
  height: 20px;
}

.spinner-medium .spinner {
  width: 32px;
  height: 32px;
}

.spinner-large .spinner {
  width: 48px;
  height: 48px;
}

/* 点状加载器 */
.spinner-dots {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.spinner-dots span {
  width: 25%;
  height: 25%;
  background: #e25a1b;
  border-radius: 50%;
  animation: dots-bounce 1.4s infinite ease-in-out;
}

.spinner-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

/* 环形加载器 */
.spinner-ring {
  width: 100%;
  height: 100%;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #e25a1b;
  border-radius: 50%;
  animation: ring-spin 1s linear infinite;
}

/* 脉冲加载器 */
.spinner-pulse {
  width: 100%;
  height: 100%;
  background: #e25a1b;
  border-radius: 50%;
  animation: pulse-scale 1.5s infinite ease-in-out;
}

/* 条形加载器 */
.spinner-bars {
  display: flex;
  gap: 2px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.spinner-bars span {
  width: 15%;
  height: 100%;
  background: #e25a1b;
  border-radius: 1px;
  animation: bars-stretch 1.2s infinite ease-in-out;
}

.spinner-bars span:nth-child(1) {
  animation-delay: -1.2s;
}

.spinner-bars span:nth-child(2) {
  animation-delay: -1.1s;
}

.spinner-bars span:nth-child(3) {
  animation-delay: -1.0s;
}

.spinner-bars span:nth-child(4) {
  animation-delay: -0.9s;
}

.spinner-bars span:nth-child(5) {
  animation-delay: -0.8s;
}

/* 文本样式 */
.spinner-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  white-space: nowrap;
}

.spinner-small .spinner-text {
  font-size: 10px;
}

.spinner-large .spinner-text {
  font-size: 14px;
}

/* 动画定义 */
@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes ring-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes bars-stretch {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* 主题变体 */
.spinner-loading.theme-light {
  --spinner-color: #e25a1b;
  --spinner-bg: #f3f3f3;
}

.spinner-loading.theme-dark {
  --spinner-color: #ff7b3d;
  --spinner-bg: #333;
}

.spinner-loading.theme-success {
  --spinner-color: #52c41a;
  --spinner-bg: #f6ffed;
}

.spinner-loading.theme-warning {
  --spinner-color: #faad14;
  --spinner-bg: #fffbe6;
}

.spinner-loading.theme-error {
  --spinner-color: #ff4d4f;
  --spinner-bg: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .spinner-large .spinner {
    width: 40px;
    height: 40px;
  }
  
  .spinner-medium .spinner {
    width: 28px;
    height: 28px;
  }
  
  .spinner-small .spinner {
    width: 18px;
    height: 18px;
  }
}

/* 无障碍访问 */
@media (prefers-reduced-motion: reduce) {
  .spinner-loading * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}
</style>
